use anyhow::{Context, Result};
use std::fs;
use std::path::Path;
use std::time::Duration;
use tokio::fs::File;
use tokio::io::{AsyncWriteExt, BufWriter};

use crate::sample::Sample;

/// A simple CSV file logger for single sensor data
pub struct CsvLogger {
    file: BufWriter<File>,
    buffer: String,
}

impl CsvLogger {
    /// Create a new CSV logger for a single sensor
    pub async fn new(path: &Path) -> Result<Self> {
        // Ensure the directory exists
        if let Some(parent) = Path::new(path).parent() {
            fs::create_dir_all(parent).context("Failed to create directory")?;
        }

        // Create the file
        let mut file = File::create(path)
            .await
            .context("Failed to create CSV file")?;

        // Write the header for single sensor
        file.write_all(b"time_offset,x,y,z\n")
            .await
            .context("Failed to write CSV header")?;
        let file = BufWriter::new(file);

        Ok(Self {
            file,
            buffer: String::new(),
        })
    }

    /// Log a single sample to the CSV file
    pub async fn log(&mut self, time_offset: Duration, sample: &Sample) -> Result<()> {
        use std::fmt::Write;

        self.buffer.clear();
        write!(
            &mut self.buffer,
            "{},{},{},{}\n",
            time_offset.as_millis(),
            sample.x,
            sample.y,
            sample.z,
        )
        .context("Failed to format sample")?;

        self.file
            .write_all(self.buffer.as_bytes())
            .await
            .context("Failed to write sample to CSV file")?;

        Ok(())
    }

    /// Flush the CSV writer
    pub async fn flush(&mut self) -> Result<()> {
        self.file
            .flush()
            .await
            .context("Failed to flush CSV file")?;
        Ok(())
    }
}

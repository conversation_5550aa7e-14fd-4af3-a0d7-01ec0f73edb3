#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/i2c.h"
#include "driver/uart.h"
#include "esp_log.h"
#include "esp_err.h"
#include <stdio.h>

#include "packet.h"

// I2C Bus 0 Configuration (Sensor 1)
#define I2C_BUS_0 I2C_NUM_0
#define I2C_BUS_0_SDA_IO 15
#define I2C_BUS_0_SCL_IO 17

// I2C Bus 1 Configuration (Sensor 2)
#define I2C_BUS_1 I2C_NUM_1
#define I2C_BUS_1_SDA_IO 12
#define I2C_BUS_1_SCL_IO 14

// Common I2C Configuration - Increased to 1MHz for higher throughput
#define I2C_MASTER_FREQ_HZ 1000000
#define I2C_MASTER_TIMEOUT_MS 1000

// UART Configuration
#define UART_PORT_NUM UART_NUM_0
#define UART_BAUD_RATE 460800
#define UART_TX_PIN 43
#define UART_RX_PIN 44
#define UART_BUF_SIZE 2048

// ADXL345 Configuration
#define ADXL345_ADDR 0x53

// ADXL345 registers
#define REG_DEVID 0x00
#define REG_BW_RATE 0x2C
#define REG_POWER_CTL 0x2D
#define REG_DATA_FORMAT 0x31
#define REG_FIFO_CTL 0x38
#define REG_FIFO_STATUS 0x39
#define REG_DATAX0 0x32

// ADXL345 ODR settings: 0x0F = 1600Hz, 0x0E = 800Hz, 0x0D = 400Hz
// Note: ADXL345 maximum is 1600Hz, NOT 3200Hz!
#define ADXL345_ODR_CODE 0x0F // 1600 Hz maximum

static const char *TAG = "FIRMW";

// Write single byte to register for a specific sensor
static esp_err_t write_reg(i2c_port_t i2c_port, uint8_t reg, uint8_t val)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_write_byte(cmd, val, true);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Read multiple bytes starting at a register
static esp_err_t read_regs(i2c_port_t i2c_port, uint8_t reg, uint8_t *buf, size_t len)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    // Send register address (with auto-increment bit for multi-byte reads)
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg | (len > 1 ? 0x80 : 0), true);
    // Read bytes
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_READ, true);
    if (len > 1)
    {
        i2c_master_read(cmd, buf, len - 1, I2C_MASTER_ACK);
    }
    i2c_master_read_byte(cmd, buf + len - 1, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Read 6 bytes starting at DATAX0 register for accelerometer data
static esp_err_t read_accel_data(i2c_port_t i2c_port, uint8_t *buf)
{
    return read_regs(i2c_port, REG_DATAX0, buf, 6);
}

// Optimized burst read for multiple samples from FIFO
static esp_err_t read_accel_burst(i2c_port_t i2c_port, uint8_t *buf, uint8_t sample_count)
{
    // Read multiple 6-byte samples in one I2C transaction
    // ADXL345 FIFO auto-increments, so we can read continuously
    return read_regs(i2c_port, REG_DATAX0, buf, sample_count * 6);
}

// Read single byte from register
static esp_err_t read_reg(i2c_port_t i2c_port, uint8_t reg, uint8_t *val)
{
    i2c_cmd_handle_t cmd = i2c_cmd_link_create();
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_WRITE, true);
    i2c_master_write_byte(cmd, reg, true);
    i2c_master_start(cmd);
    i2c_master_write_byte(cmd, (ADXL345_ADDR << 1) | I2C_MASTER_READ, true);
    i2c_master_read_byte(cmd, val, I2C_MASTER_NACK);
    i2c_master_stop(cmd);
    esp_err_t err = i2c_master_cmd_begin(i2c_port, cmd, I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    i2c_cmd_link_delete(cmd);
    return err;
}

// Initialize a specific I2C bus
static esp_err_t i2c_bus_init(i2c_port_t i2c_port, int sda_io, int scl_io)
{
    i2c_config_t cfg = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = sda_io,
        .scl_io_num = scl_io,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = I2C_MASTER_FREQ_HZ,
    };
    esp_err_t err = i2c_param_config(i2c_port, &cfg);
    if (err != ESP_OK)
        return err;
    return i2c_driver_install(i2c_port, cfg.mode, 0, 0, 0);
}

// Initialize all I2C buses
static esp_err_t i2c_master_init(void)
{
    esp_err_t err;

    // Initialize I2C Bus 0
    err = i2c_bus_init(I2C_BUS_0, I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 0: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 0 initialized (SDA=%d, SCL=%d)", I2C_BUS_0_SDA_IO, I2C_BUS_0_SCL_IO);

    // Initialize I2C Bus 1
    err = i2c_bus_init(I2C_BUS_1, I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize I2C Bus 1: %s", esp_err_to_name(err));
        return err;
    }
    ESP_LOGI(TAG, "I2C Bus 1 initialized (SDA=%d, SCL=%d)", I2C_BUS_1_SDA_IO, I2C_BUS_1_SCL_IO);

    return ESP_OK;
}

// Initialize UART for data transmission
static esp_err_t uart_init(void)
{
    uart_config_t uart_config = {
        .baud_rate = UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };

    esp_err_t err = uart_driver_install(UART_PORT_NUM, UART_BUF_SIZE, UART_BUF_SIZE, 0, NULL, 0);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to install UART driver: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_param_config(UART_PORT_NUM, &uart_config);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to configure UART: %s", esp_err_to_name(err));
        return err;
    }

    err = uart_set_pin(UART_PORT_NUM, UART_TX_PIN, UART_RX_PIN, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to set UART pins: %s", esp_err_to_name(err));
        return err;
    }

    ESP_LOGI(TAG, "UART initialized: %d baud, TX=%d, RX=%d", UART_BAUD_RATE, UART_TX_PIN, UART_RX_PIN);
    return ESP_OK;
}

// Initialize a specific ADXL345 sensor
static esp_err_t adxl345_init(i2c_port_t i2c_port, uint8_t sensor_id)
{
    uint8_t id = 0;
    esp_err_t err = read_reg(i2c_port, REG_DEVID, &id);

    if (err != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read DEVID from sensor %d: %s", sensor_id, esp_err_to_name(err));
        return ESP_FAIL;
    }

    if (id != 0xE5)
    {
        ESP_LOGE(TAG, "Invalid DEVID on sensor %d: expected 0xE5, got 0x%02X", sensor_id, id);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "ADXL345 sensor %d detected (DEVID=0x%02X)", sensor_id, id);

    // Set output data rate to 1600 Hz (0x0F = maximum for ADXL345)
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_BW_RATE, ADXL345_ODR_CODE));
    // ±16 g, full-resolution
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_DATA_FORMAT, 0x0B));
    // Enable measurement mode
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_POWER_CTL, 0x08));
    // FIFO: stream mode (10), watermark = BURST_SAMPLES
    ESP_ERROR_CHECK(write_reg(i2c_port, REG_FIFO_CTL, (2 << 6) | BURST_SAMPLES));

    ESP_LOGI(TAG, "Sensor %d initialized: ±16g @1600Hz, FIFO burst=%d", sensor_id, BURST_SAMPLES);
    return ESP_OK;
}

// Calculate simple checksum for data integrity
static uint16_t calculate_checksum(const uint8_t *data, size_t len)
{
    uint16_t checksum = 0;
    for (size_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }
    return checksum;
}

// Send sensor data via UART in binary format (variable number of samples)
static void send_sensor_data(uint8_t sensor_id, uint32_t timestamp, int16_t accel_data[BURST_SAMPLES][3], uint8_t sample_count)
{
    uart_packet_t packet;

    // Set magic header
    packet.header[0] = 'A';
    packet.header[1] = 'D';
    packet.header[2] = 'X';
    packet.header[3] = 'L';

    // Copy sensor data
    packet.sensor_id = sensor_id;
    packet.timestamp = timestamp;
    packet.sample_count = sample_count;

    // Copy accelerometer data directly
    for (int i = 0; i < sample_count; i++)
    {
        packet.accel[i][0] = accel_data[i][0];
        packet.accel[i][1] = accel_data[i][1];
        packet.accel[i][2] = accel_data[i][2];
    }

    // Calculate checksum (exclude checksum field itself)
    packet.checksum = calculate_checksum((uint8_t *)&packet, sizeof(packet) - sizeof(packet.checksum));

    // Send entire packet in one UART write for maximum efficiency
    int bytes_written = uart_write_bytes(UART_PORT_NUM, &packet, sizeof(packet));
    if (bytes_written != sizeof(packet))
    {
        ESP_LOGW(TAG, "UART write incomplete: %d/%d bytes for sensor %d", bytes_written, sizeof(packet), sensor_id);
    }
}

void app_main(void)
{
    ESP_LOGI(TAG, "Starting high-performance dual ADXL345 sensor firmware");

    // Initialize I2C buses
    ESP_ERROR_CHECK(i2c_master_init());

    // Initialize UART for data transmission
    ESP_ERROR_CHECK(uart_init());

    // Initialize both sensors
    ESP_LOGI(TAG, "Initializing sensors...");
    if (adxl345_init(I2C_BUS_0, 1) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize sensor 1");
        // sleep
        vTaskDelay(pdMS_TO_TICKS(1000));
        abort();
    }

    if (adxl345_init(I2C_BUS_1, 2) != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to initialize sensor 2");
        vTaskDelay(pdMS_TO_TICKS(1000));
        abort();
    }

    ESP_LOGI(TAG, "Both sensors initialized successfully");
    ESP_LOGI(TAG, "Starting main loop - alternating FIFO reads @1600Hz per sensor");

    uint8_t current_sensor = 1;
    uint8_t raw_data[6];
    int16_t accel_burst[BURST_SAMPLES][3];
    uint32_t last_log_time = 0;
    size_t total_samples = 0;
    size_t sensor1_samples = 0;
    size_t sensor2_samples = 0;
    size_t fifo_reads = 0;

    while (1)
    {
        i2c_port_t i2c_port = (current_sensor == 1) ? I2C_BUS_0 : I2C_BUS_1;

        // Check FIFO status
        uint8_t fifo_status;
        esp_err_t err = read_regs(i2c_port, REG_FIFO_STATUS, &fifo_status, 1);

        if (err != ESP_OK)
        {
            ESP_LOGW(TAG, "Failed to read FIFO status from sensor %d: %s", current_sensor, esp_err_to_name(err));
            // Switch to other sensor on error to avoid getting stuck
            current_sensor = (current_sensor == 1) ? 2 : 1;
            vTaskDelay(pdMS_TO_TICKS(10));
            continue;
        }

        // Check for FIFO overflow and reset if needed
        if (fifo_status & 0x80)
        {
            ESP_LOGW(TAG, "FIFO overflow on sensor %d! Resetting FIFO...", current_sensor);
            write_reg(i2c_port, REG_FIFO_CTL, 0x00); // Disable FIFO
            vTaskDelay(pdMS_TO_TICKS(1));
            write_reg(i2c_port, REG_FIFO_CTL, (2 << 6) | BURST_SAMPLES); // Re-enable stream mode
            current_sensor = (current_sensor == 1) ? 2 : 1;
            continue;
        }

        uint8_t samples_available = fifo_status & 0x3F;

        // Read whatever samples are available in the FIFO
        if (samples_available > 0)
        {
            fifo_reads++;
            uint32_t timestamp = xTaskGetTickCount();
            uint8_t samples_read = 0;

            // Read all available samples from FIFO (up to BURST_SAMPLES max)
            uint8_t samples_to_read = (samples_available > BURST_SAMPLES) ? BURST_SAMPLES : samples_available;

            // Read samples one by one (safer approach)
            for (int i = 0; i < samples_to_read; i++)
            {
                err = read_accel_data(i2c_port, raw_data);
                if (err == ESP_OK)
                {
                    // Convert raw data to signed 16-bit values
                    accel_burst[i][0] = (int16_t)((raw_data[1] << 8) | raw_data[0]);
                    accel_burst[i][1] = (int16_t)((raw_data[3] << 8) | raw_data[2]);
                    accel_burst[i][2] = (int16_t)((raw_data[5] << 8) | raw_data[4]);
                    samples_read++;
                }
                else
                {
                    ESP_LOGW(TAG, "Failed to read sample %d from sensor %d", i, current_sensor);
                    break;
                }
            }

            // Send data if we got any samples
            if (samples_read > 0)
            {
                send_sensor_data(current_sensor, timestamp, accel_burst, samples_read);
                total_samples += samples_read;
                if (current_sensor == 1)
                {
                    sensor1_samples += samples_read;
                }
                else
                {
                    sensor2_samples += samples_read;
                }
            }
        }

        // Always switch to the other sensor (don't get stuck on one sensor)
        current_sensor = (current_sensor == 1) ? 2 : 1;

        // Log performance stats every second
        uint32_t current_time = xTaskGetTickCount();
        if (current_time - last_log_time >= pdMS_TO_TICKS(1000))
        {
            ESP_LOGI(TAG, "Total: %zu samples/sec (S1: %zu, S2: %zu, FIFO reads: %zu)",
                     total_samples, sensor1_samples, sensor2_samples, fifo_reads);
            ESP_LOGI(TAG, "Expected max: 3200 total (1600 per sensor)");
            total_samples = 0;
            sensor1_samples = 0;
            sensor2_samples = 0;
            fifo_reads = 0;
            last_log_time = current_time;
        }
    }
}
